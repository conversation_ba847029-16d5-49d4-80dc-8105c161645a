<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI: Focus, Track, Achieve</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>

<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex flex-col min-h-screen">
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Achieve</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Goals & Progress</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="20" height="20">
<path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>
<path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
</svg>
</button>
</div>
</header>
<main class="flex-grow p-4 space-y-6">
<section class="bg-white dark:bg-gray-800/20 rounded-xl shadow-sm overflow-hidden">
<div class="h-40 bg-cover bg-center" style="
              background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuB3SlITj8k4luxJacmGoYHkjnn33NetDBk5NkUmkdvX0GPpFZkuLQhe8KEXfNGEbQA-lG24ZNYGG5bF7FdDMGoMDQw_HxqRLsrZMcanqkcUUqKq3N5sX_ent4Z7cbs9MPdO2c0ZR3CNbLir--dWxd9_vw4Ir7GL99pOd_kaz6OiPVjtMWt9S6_DQNv3u2K9iVogjQn2dVeRDtIav1Sn1txko1DTZFscaMqSk0ajnA8pjLZAGNko_t4yM4zh7Gsv-HWCYvEhZnGsS5E');
            "></div>
<div class="p-4">
<h2 class="text-lg font-bold text-gray-900 dark:text-white">
              Complete your goal
            </h2>
<p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Complete all your tasks to achieve your goal.
            </p>
<div class="flex items-center gap-4 mt-3">
<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
<div class="bg-primary h-2 rounded-full" style="width: 100%"></div>
</div>
<span class="text-sm font-semibold text-gray-900 dark:text-white">100%</span>
</div>
</div>
</section>
<section>
<div class="flex items-center justify-between mb-4">
<h2 class="text-xl font-bold text-gray-900 dark:text-white">
              Subjects
            </h2>
<select class="form-select w-40 rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800/20 text-gray-900 dark:text-white focus:border-primary focus:ring-primary">
<option>All Subjects</option>
<option>Mathematics</option>
<option>Physics</option>
<option>Chemistry</option>
</select>
</div>
<div class="space-y-4">
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuC3mTFBz_vrVT137JPuhlfsj-k2YolMlsq4a3b6KMdhLEGwClrH-W_db4Q3O6aiQ3qzRejErGRvas53h3DkKv_Ql8l5N-trTl7mwdHLfc1w0n2T9sge0HArBMdw3VDv4RKDdW6fzbQ74TTnYh-gfqQbNziZOMzLhCZ_KnGOQBXppvOLjA6_BEGOzWND1WbutP07WlrU93Ou-CuYAJSwAUOHaYBbGwqlvgljHwzP5slBjIHYJVa7YqQTnrAeFPeFpK3iVWVStZF3OC8');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">
                  Mathematics
                </p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400 dark:text-gray-500">
<path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/>
</svg>
</div>
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBnyIpSaSLMeVOptTipylwYOAo_QaPQE60pl-ZFkZHRBFaroFhlV_-FVno9-FDsiGBtDPVxjyENZe5rDEoakYpxQdGjrybpUFkYdVmtr1-kfwUnso-QPkj5-QZOhhpFiBdU9Ka-LsgmPsnsnWc8miK-Tna_HpEJl1wDL3VMGyqDnbh2RVAaG3w3-kzXzE8GzhAd-03urg343jpl2o0JUFd1Pivd-7FnUTzveRGbgKdt1WbPUB4bbXQryTJFPpcnq09hxiUnE734Bjk');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">Physics</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400 dark:text-gray-500">
<path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/>
</svg>
</div>
<div class="flex items-center gap-4 p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-16 h-16 rounded-lg bg-cover bg-center flex-shrink-0" style="
                  background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCRmZ4n6wwu-5KknThFNloVOgYNmJ4f6jH2S9Pfp3kJuf0uA33dQOBGUoxlO6w23CDnALGo6gNfqo74oNYCYZ4oGHzhs5Xmert1G54dLfYQ9yQ2MqwmSsIvVBfG0lYWMGd9iIeMBNNAeGxf9hnz8agYGBZSHqQ4rqfcjmmJvr2jxV3BPvY9PWv1FbOR1ABrY9ByjdweZiwaoNJcpeVS5Hv7jrU8x_lpmiRDH1Sy7lPcn71yoDUZBsDi2fP8L8T44cLo6DVtppiKF3w');
                "></div>
<div class="flex-grow">
<p class="font-bold text-gray-900 dark:text-white">Chemistry</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
                  100% completed
                </p>
</div>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-gray-400 dark:text-gray-500">
<path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/>
</svg>
</div>
</div>
</section>
<section>
<h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Exams &amp; Mock Tests
          </h2>
<div class="grid grid-cols-2 gap-4">
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDeqdOuI8Ro16cZZc-A9peijDAOSO-X-D85uFk8sWDJRPrDIvPVoPGSCWm2L7Vn-dpq-d2ivoGfmSL9aXlZgY6Y7nXY_7lZrRpt5kAxAjJdJ2NiOf8S0iLdxJH-zyB0U32H3v6EdJ2Vgy66Mj2sV3tfFsWgAbwldIGEzUyQzKlU7y2JrYOt1Da_5Js7zIbDfwwcgl13qKYXVADE0IawX5Vi95F_ZpwfUrjebTRCO4271SayMtyYuUJ3svyy-w7wEqEOHn1H0a5FgKY')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">JEE Main</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBBWfTZ1Tq0OBnuyvYIsfWntcN_dDAv8kcyzO4RL4GLDA7xHGLWbOswtRdLoXa4xTc5DcKPmw0w_1sp3KpLvoySM4xdvH0WvQG0y6mbQmNqDweRNijFTnVanv8O3ZqZXkFWfUT4MhjhLyupMSMBoTHmupmLdafn5UDS_fhE71BZJT4EsgsSJR6Q58YYO2y5GZ92rSh7u8II_ZC7s_rInW17XsRqAtRst1_moriqDmua01UnIT17J69pLffB8hqC24u7yik2Ka09YAs')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">JEE Advanced</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
</div>
</section>
<section>
<h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Study Plans
          </h2>
<div class="grid grid-cols-2 gap-4">
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBXViybLg7q6BOq_zCpu-KloaZccQqbl8_ED47GyEgYAbRQ19gWOUJWf79Gc3liej433seYOMEC6jluQoDbIPnuBnrpzgwXrBJku6eYQp-xqoQg359LflSRvZGEm95SO4IK0YmBrQfXf4ysUOeUnwV0bbXyFDFBydQOzG46-4GbexiT99-0wG1vnzdtveouFOQ2qpIV8gwXGYITBtk3R3NMnw2eixXDM2GKC-IOnZy-nK0e90zPMRtR6HvWk8GOUMEKm88gFa7aQPw')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">Daily Plan</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
<div class="p-3 bg-white dark:bg-gray-800/20 rounded-xl">
<div class="w-full h-20 rounded-lg bg-cover bg-center" style="background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuChSd1YdHQVFCLhn5ZlvAO6Fafi26bbvRBEp6EaAZ2DZjhkiTw3Nq6DA0P-SV7R-ab9dEHk1rb2bC3e3Xqjo0gMlVGG67CboNMTk6XTjB6ekFc9Xz2zyW5yK157eu2AsX4j1noftmRawXfIOceNa7FbOyXyJD6-fOqHT59FJ50uRy1Y_tbyjAa1nhCElV2E_EgTYePlw9ur4XfVtzt1--sOrGDXqfFezFx3u6V8C7p8ZWrJ7nmGHq5cDWQG_H2SOApGAyaLLve5-vM')"></div>
<p class="mt-2 font-bold text-gray-900 dark:text-white text-sm">Weekly Plan</p>
<p class="text-xs text-gray-500 dark:text-gray-400">100% completed</p>
</div>
</div>
</section>
</main>
<footer class="fixed bottom-0 w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/>
</svg>
</div>
<p class="text-xs font-medium">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="achieve.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"/>
</svg>
</div>
<p class="text-xs font-semibold">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>