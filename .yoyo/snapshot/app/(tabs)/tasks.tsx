import { ThemeProvider } from '@/context/theme-context';
import { useDarkMode } from '@/hooks/use-dark-mode';
import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';

function TasksContent() {
  const { isDark } = useDarkMode();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f7f6' }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>Tasks</Text>
          <Text style={[styles.subtitle, { color: isDark ? '#cccccc' : '#666666' }]}>
            Task management and to-dos
          </Text>
        </View>

        <View style={styles.content}>
          <Text style={[styles.description, { color: isDark ? '#cccccc' : '#666666' }]}>
            This is where your task management features will be implemented.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

export default function TasksScreen() {
  return (
    <ThemeProvider>
      <SafeAreaProvider>
        <TasksContent />
      </SafeAreaProvider>
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  content: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
});
