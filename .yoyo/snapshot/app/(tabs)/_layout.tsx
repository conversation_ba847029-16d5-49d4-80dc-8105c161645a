import { Icon, Label, NativeTabs } from 'expo-router/unstable-native-tabs';
import React from 'react';
import { DynamicColorIOS, Platform } from 'react-native';

export default function TabLayout() {
  return (
    <NativeTabs position="bottom"
      minimizeBehavior="onScrollDown"
      // Enable liquid glass effect on iOS 26
      disableTransparentOnScrollEdge={false}
      tintColor={Platform.OS === 'ios' ? DynamicColorIOS({
        dark: '#F4C753',
        light: '#F4C753',
      }) : '#F4C753'}
      // Since we're hiding labels, we can remove labelStyle
      >
      <NativeTabs.Trigger name="index">
        <Label hidden />
        <Icon
          sf={{ default: 'house', selected: 'house.fill' }}
          drawable="ic_home"
        />
      </NativeTabs.Trigger>
      <NativeTabs.Trigger name="focus">
        <Label hidden />
        <Icon
          sf={{ default: 'timer', selected: 'timer.circle.fill' }}
          drawable="ic_timer"
        />
      </NativeTabs.Trigger>
      <NativeTabs.Trigger name="track">
        <Label hidden />
        <Icon
          sf={{ default: 'chart.bar', selected: 'chart.bar.fill' }}
          drawable="ic_bar_chart"
        />
      </NativeTabs.Trigger>
      <NativeTabs.Trigger name="achieve">
        <Label hidden />
        <Icon
          sf={{ default: 'trophy', selected: 'trophy.fill' }}
          drawable="ic_trophy"
        />
      </NativeTabs.Trigger>
      <NativeTabs.Trigger name="tasks">
        <Label hidden />
        <Icon
          sf={{ default: 'list.bullet', selected: 'list.bullet.circle.fill' }}
          drawable="ic_list"
        />
      </NativeTabs.Trigger>
    </NativeTabs>
  );
}
