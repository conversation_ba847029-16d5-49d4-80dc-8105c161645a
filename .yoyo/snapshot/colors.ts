import { Platform } from 'react-native';

const IOS_SYSTEM_COLORS = {
white: 'rgb(255, 255, 255)',
black: 'rgb(0, 0, 0)',
light: {
  grey6: 'rgb(251, 251, 251)',
  grey5: 'rgb(241, 241, 240)',
  grey4: 'rgb(231, 232, 229)',
  grey3: 'rgb(216, 217, 213)',
  grey2: 'rgb(185, 188, 181)',
  grey: 'rgb(165, 169, 160)',
  background: 'rgb(251, 252, 250)',
  foreground: 'rgb(0, 0, 0)',
  root: 'rgb(251, 252, 250)',
  card: 'rgb(251, 252, 250)',
  destructive: 'rgb(255, 56, 43)',
  primary: 'rgb(0, 30, 127)',
},
dark: {
  grey6: 'rgb(30, 31, 28)',
  grey5: 'rgb(50, 52, 47)',
  grey4: 'rgb(63, 66, 59)',
  grey3: 'rgb(84, 87, 79)',
  grey2: 'rgb(127, 133, 120)',
  grey: 'rgb(165, 169, 160)',
  background: 'rgb(0, 0, 0)',
  foreground: 'rgb(255, 255, 254)',
  root: 'rgb(0, 0, 0)',
  card: 'rgb(0, 0, 0)',
  destructive: 'rgb(254, 67, 54)',
  primary: 'rgb(150, 255, 3)',
},
} as const;

const ANDROID_COLORS = {
white: 'rgb(255, 255, 255)',
black: 'rgb(0, 0, 0)',
light: {
  grey6: 'rgb(253, 255, 250)',
  grey5: 'rgb(248, 251, 243)',
  grey4: 'rgb(243, 248, 236)',
  grey3: 'rgb(241, 246, 233)',
  grey2: 'rgb(238, 245, 229)',
  grey: 'rgb(236, 243, 226)',
  background: 'rgb(254, 255, 252)',
  foreground: 'rgb(1, 1, 1)',
  root: 'rgb(254, 255, 252)',
  card: 'rgb(254, 255, 252)',
  destructive: 'rgb(186, 26, 26)',
  primary: 'rgb(0, 30, 127)',
},
dark: {
  grey6: 'rgb(31, 36, 25)',
  grey5: 'rgb(39, 45, 31)',
  grey4: 'rgb(45, 51, 35)',
  grey3: 'rgb(50, 58, 38)',
  grey2: 'rgb(53, 61, 40)',
  grey: 'rgb(58, 68, 44)',
  background: 'rgb(23, 26, 19)',
  foreground: 'rgb(233, 237, 227)',
  root: 'rgb(23, 26, 19)',
  card: 'rgb(23, 26, 19)',
  destructive: 'rgb(147, 0, 10)',
  primary: 'rgb(150, 255, 3)',
},
} as const;

const WEB_COLORS = {
white: 'rgb(255, 255, 255)',
black: 'rgb(0, 0, 0)',
light: {
  grey6: 'rgb(245, 246, 245)',
  grey5: 'rgb(235, 236, 234)',
  grey4: 'rgb(225, 226, 223)',
  grey3: 'rgb(210, 212, 207)',
  grey2: 'rgb(180, 183, 175)',
  grey: 'rgb(160, 163, 154)',
  background: 'rgb(251, 254, 246)',
  foreground: 'rgb(31, 33, 28)',
  root: 'rgb(251, 254, 246)',
  card: 'rgb(251, 254, 246)',
  destructive: 'rgb(186, 26, 26)',
  primary: 'rgb(15, 25, 0)',
},
dark: {
  grey6: 'rgb(30, 31, 28)',
  grey5: 'rgb(50, 52, 47)',
  grey4: 'rgb(63, 66, 59)',
  grey3: 'rgb(84, 87, 79)',
  grey2: 'rgb(127, 133, 120)',
  grey: 'rgb(165, 169, 160)',
  background: 'rgb(21, 25, 17)',
  foreground: 'rgb(230, 236, 223)',
  root: 'rgb(21, 25, 17)',
  card: 'rgb(21, 25, 17)',
  destructive: 'rgb(147, 0, 10)',
  primary: 'rgb(150, 255, 3)',
},
} as const;

const COLORS =
Platform.OS === 'ios'
  ? IOS_SYSTEM_COLORS
  : Platform.OS === 'android'
    ? ANDROID_COLORS
    : WEB_COLORS;

export { COLORS };
