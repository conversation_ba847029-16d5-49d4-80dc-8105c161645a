import React from 'react';
import { View, ScrollView } from 'react-native';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { StatCard } from '@/components/ui/stat-card';
import { Button } from '@/components/ui/button';
import { Bar<PERSON>hart } from '@/components/ui/bar-chart';
import { Heading1, Heading3, BodyText, Caption } from '@/components/ui/typography';

const testData = [
  { height: 60, label: 'Mon' },
  { height: 80, label: 'Tue' },
  { height: 95, label: 'Wed', isActive: true },
  { height: 50, label: 'Thu' },
  { height: 70, label: 'Fri' },
  { height: 40, label: 'Sat' },
  { height: 20, label: 'Sun' },
];

export function DashboardFixesTest() {
  return (
    <ScrollView 
      className="flex-1 bg-background-light dark:bg-background-dark"
      contentContainerStyle={{ padding: 16, paddingBottom: 100 }}
    >
      <Heading1 className="mb-6">Dashboard Fixes Test</Heading1>

      {/* Test 1: Dark Mode Card Visibility */}
      <DashboardCard className="mb-6">
        <Heading3 className="mb-4">✅ Dark Mode Card Visibility</Heading3>
        <BodyText>This card should be clearly visible in both light and dark modes.</BodyText>
        <Caption className="mt-2">Background opacity increased from 94% to 100%</Caption>
      </DashboardCard>

      {/* Test 2: Button Alignment and Text Color */}
      <DashboardCard className="mb-6">
        <View className="flex-row items-center justify-between mb-4">
          <Heading3 className="flex-1">✅ Button Test</Heading3>
          <Button variant="primary" size="md">
            Focus
          </Button>
        </View>
        <BodyText>Button text should be black on yellow background for better contrast.</BodyText>
      </DashboardCard>

      {/* Test 3: StatCard Visibility */}
      <DashboardCard className="mb-6">
        <Heading3 className="mb-4">✅ StatCard Visibility Test</Heading3>
        <View className="space-y-4 sm:space-y-0 sm:flex-row sm:gap-4">
          <View className="w-full sm:flex-1">
            <StatCard value="2.5h" label="Focus Time" />
          </View>
          <View className="w-full sm:flex-1">
            <StatCard value="85%" label="Completion" />
          </View>
        </View>
        <Caption className="mt-2">StatCards should be clearly visible in dark mode</Caption>
      </DashboardCard>

      {/* Test 4: Bar Chart and Day Labels */}
      <DashboardCard className="mb-6">
        <Heading3 className="mb-4">✅ Bar Chart & Labels Test</Heading3>
        <View className="w-full bg-surface-light/80 dark:bg-surface-dark/80 rounded-xl p-4">
          <View className="h-40 mb-4">
            <BarChart data={testData} className="h-full" />
          </View>
          
          <View className="flex-row justify-between px-1">
            {testData.map((day, index) => (
              <View key={index} className="flex-1 items-center">
                <Caption className={day.isActive ? 'text-primary font-bold' : ''}>
                  {day.label}
                </Caption>
              </View>
            ))}
          </View>
        </View>
        <Caption className="mt-2">
          • Bars should be visible with minimum 8% height
          • Day labels should not overflow
          • Chart background should be visible in dark mode
        </Caption>
      </DashboardCard>

      {/* Test 5: Responsive Layout */}
      <DashboardCard className="mb-6">
        <Heading3 className="mb-4">✅ Responsive Layout Test</Heading3>
        <BodyText className="mb-4">
          Cards should stack vertically on mobile and arrange horizontally on larger screens.
        </BodyText>
        
        <View className="space-y-4 md:space-y-0 md:flex-row md:gap-4">
          <View className="w-full md:flex-1">
            <StatCard value="Card 1" label="Mobile: Stacked" />
          </View>
          <View className="w-full md:flex-1">
            <StatCard value="Card 2" label="Desktop: Side by side" />
          </View>
        </View>
      </DashboardCard>

      {/* Test 6: Typography Contrast */}
      <DashboardCard className="mb-6">
        <Heading3 className="mb-4">✅ Typography Contrast Test</Heading3>
        <View className="space-y-2">
          <BodyText>Primary text should be clearly readable</BodyText>
          <BodyText color="secondary">Secondary text should have good contrast</BodyText>
          <Caption>Caption text should be visible but subtle</Caption>
        </View>
      </DashboardCard>

      {/* Summary */}
      <DashboardCard>
        <Heading3 className="mb-4">🎉 All Fixes Applied</Heading3>
        <View className="space-y-2">
          <BodyText>✅ Dark mode card visibility improved</BodyText>
          <BodyText>✅ Button text contrast fixed</BodyText>
          <BodyText>✅ Graph visibility enhanced</BodyText>
          <BodyText>✅ Day labels properly contained</BodyText>
          <BodyText>✅ StatCard backgrounds visible</BodyText>
          <BodyText>✅ Responsive layouts working</BodyText>
        </View>
      </DashboardCard>
    </ScrollView>
  );
}
