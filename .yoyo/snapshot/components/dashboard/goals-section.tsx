import { DashboardCard } from '@/components/ui/dashboard-card';
import { ProgressBar } from '@/components/ui/progress-bar';
import React from 'react';
import { Text, View } from 'react-native';

interface Goal {
  title: string;
  progress: number;
  color: string;
}

const goals: Goal[] = [
  {
    title: "Master Calculus",
    progress: 75,
    color: "bg-system-blue"
  },
  {
    title: "Ace Physics Exam",
    progress: 50,
    color: "bg-system-green"
  }
];

export function GoalsSection() {
  return (
    <DashboardCard>
      <Text className="text-xl font-bold mb-6 text-label-primary-light dark:text-label-primary-dark">
        Achieve Your Goals
      </Text>

      <View className="space-y-6">
        {goals.map((goal, index) => (
          <View key={index} className="space-y-3">
            <View className="flex-row items-center justify-between">
              <Text className="font-medium text-label-primary-light dark:text-label-primary-dark">
                {goal.title}
              </Text>
              <Text className="font-medium text-label-secondary-light/68 dark:text-label-secondary-dark/68">
                {goal.progress}%
              </Text>
            </View>

            <ProgressBar
              progress={goal.progress}
              barClassName={goal.color}
              delay={index * 200}
            />
          </View>
        ))}
      </View>
    </DashboardCard>
  );
}
