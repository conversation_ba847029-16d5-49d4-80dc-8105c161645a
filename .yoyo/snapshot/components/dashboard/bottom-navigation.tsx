import { Caption } from '@/components/ui/typography';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { Platform, Pressable, View } from 'react-native';
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Conditional import for glass effect
let GlassView: any = null;
let isLiquidGlassAvailable: any = null;

try {
  const glassModule = require('expo-glass-effect');
  GlassView = glassModule.GlassView;
  isLiquidGlassAvailable = glassModule.isLiquidGlassAvailable;
} catch (error) {
  // Glass effect not available, will use fallback
}

interface NavItem {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  active?: boolean;
}

const navItems: NavItem[] = [
  { id: 'home', label: 'Home', icon: 'home', active: true },
  { id: 'focus', label: 'Focus', icon: 'time' },
  { id: 'track', label: 'Track', icon: 'bar-chart' },
  { id: 'achieve', label: 'Achieve', icon: 'trophy' },
  { id: 'tasks', label: 'Tasks', icon: 'list' },
];

export function BottomNavigation() {
  const insets = useSafeAreaInsets();
  const [glassSupported, setGlassSupported] = useState(false);

  // Check if liquid glass is available
  useEffect(() => {
    if (Platform.OS === 'ios' && isLiquidGlassAvailable) {
      setGlassSupported(isLiquidGlassAvailable());
    }
  }, []);

  const NavItemComponent = ({ item }: { item: NavItem }) => {
    const scale = useSharedValue(1);
    const translateY = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        transform: [
          { scale: scale.value },
          { translateY: translateY.value }
        ],
      };
    });

    const handlePressIn = () => {
      scale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
      translateY.value = withSpring(-2, { damping: 15, stiffness: 300 });
    };

    const handlePressOut = () => {
      scale.value = withSpring(1, { damping: 15, stiffness: 300 });
      translateY.value = withSpring(0, { damping: 15, stiffness: 300 });
    };

    return (
      <Animated.View style={animatedStyle}>
        <Pressable
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          className={`flex-col items-center gap-1.5 p-3 rounded-2xl min-w-[60px] ${
            item.active ? 'bg-primary/20' : ''
          }`}
          style={item.active ? {
            shadowColor: '#F4C753',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 4,
          } : {}}
          accessible={true}
          accessibilityRole="tab"
          accessibilityLabel={item.label}
          accessibilityState={{ selected: item.active }}
        >
          <View className="p-1">
            <Ionicons
              name={item.icon}
              size={24}
              color={item.active ? '#F4C753' : '#9CA3AF'}
            />
          </View>
          <Caption
            className={item.active ? 'text-primary font-semibold' : 'text-label-secondary-light/68 dark:text-label-secondary-dark/68'}
          >
            {item.label}
          </Caption>
        </Pressable>
      </Animated.View>
    );
  };

  // Render with glass effect if supported, fallback otherwise
  const renderContainer = (children: React.ReactNode) => {
    if (glassSupported && GlassView) {
      return (
        <GlassView
          glassEffectStyle="regular"
          className="sticky bottom-0 border-t border-separator-light/20 dark:border-separator-dark/20"
          style={{
            paddingBottom: insets.bottom,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.15,
            shadowRadius: 12,
            elevation: 12,
          }}
        >
          {children}
        </GlassView>
      );
    }

    // Fallback for non-iOS or unsupported devices
    return (
      <View
        className="sticky bottom-0 bg-background-light/96 dark:bg-background-dark/96 backdrop-blur-xl border-t border-separator-light/24 dark:border-separator-dark/24"
        style={{
          paddingBottom: insets.bottom,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        {children}
      </View>
    );
  };

  return renderContainer(
    <View className="flex-row justify-around items-center px-4 py-4">
      {navItems.map((item) => (
        <NavItemComponent key={item.id} item={item} />
      ))}
    </View>
  );
}
