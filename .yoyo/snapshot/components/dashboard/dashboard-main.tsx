import { BodyText, Heading1 } from '@/components/ui/typography';
import { ThemeProvider } from '@/context/theme-context';
import { useDarkMode } from '@/hooks/use-dark-mode';
import React from 'react';
import { ScrollView, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AnalyticsCards } from './analytics-cards';

import { GoalsSection } from './goals-section';
import { DashboardHeader } from './header';
import { NextUp } from './next-up';
import { ProgressSection } from './progress-section';
import { QuickStart } from './quick-start';
import { TasksHabits } from './tasks-habits';

function DashboardContent() {
  const { isDark } = useDarkMode();

  return (
    <View
      className="flex-1 bg-background-light dark:bg-background-dark"
      style={{
        backgroundColor: isDark ? '#000000' : '#f8f7f6'
      }}
    >
      <DashboardHeader />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingVertical: 24,
          paddingBottom: 24 // Reduced padding since native tabs handle spacing
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Section */}
        <View className="space-y-2 mb-8 px-2">
          <Heading1 className="leading-tight">
            Good morning, Friend!
          </Heading1>
          <BodyText color="secondary" className="leading-relaxed">
            Ready to achieve your goals today?
          </BodyText>
        </View>

        {/* Dashboard Sections */}
        <View className="space-y-6">
          <QuickStart />
          <ProgressSection />
          <AnalyticsCards />
          <NextUp />
          <GoalsSection />
          <TasksHabits />
        </View>
      </ScrollView>


    </View>
  );
}

export function DashboardMain() {
  return (
    <ThemeProvider>
      <SafeAreaProvider>
        <DashboardContent />
      </SafeAreaProvider>
    </ThemeProvider>
  );
}