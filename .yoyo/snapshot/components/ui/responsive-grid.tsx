import React from 'react';
import { View, Dimensions } from 'react-native';
import { cn } from '@/lib/utils';

const { width: screenWidth } = Dimensions.get('window');

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  spacing?: number;
  minCardWidth?: number;
  maxColumns?: number;
}

export function ResponsiveGrid({ 
  children, 
  className,
  spacing = 16,
  minCardWidth = 280,
  maxColumns = 3
}: ResponsiveGridProps) {
  
  // Calculate optimal number of columns based on screen width
  const getColumns = () => {
    const availableWidth = screenWidth - 48; // Account for container padding
    const cardWithSpacing = minCardWidth + spacing;
    const possibleColumns = Math.floor(availableWidth / cardWithSpacing);
    
    // Force single column on mobile
    if (screenWidth < 768) return 1;
    
    return Math.min(possibleColumns, maxColumns);
  };

  const columns = getColumns();
  const isSingleColumn = columns === 1;

  return (
    <View 
      className={cn(
        "w-full",
        isSingleColumn ? "space-y-4" : "flex-row flex-wrap",
        className
      )}
      style={{
        gap: isSingleColumn ? 0 : spacing,
      }}
    >
      {children}
    </View>
  );
}

interface ResponsiveCardWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveCardWrapper({ children, className }: ResponsiveCardWrapperProps) {
  return (
    <View className={cn("w-full mb-4 md:mb-0", className)}>
      {children}
    </View>
  );
}
