import { cn } from '@/lib/utils';
import React from 'react';
import { Pressable, View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withTiming } from 'react-native-reanimated';

interface BarData {
  height: number; // 0-100 percentage
  label?: string;
  isActive?: boolean;
}

interface BarChartProps {
  data: BarData[];
  className?: string;
  onBarPress?: (index: number) => void;
}

export function BarChart({ data, className, onBarPress }: BarChartProps) {
  return (
    <View className={cn("flex-row items-end justify-between space-x-2", className)}>
      {data.map((bar, index) => (
        <BarItem
          key={index}
          height={bar.height}
          isActive={bar.isActive}
          delay={index * 100}
          onPress={() => onBarPress?.(index)}
        />
      ))}
    </View>
  );
}

interface BarItemProps {
  height: number;
  isActive?: boolean;
  delay?: number;
  onPress?: () => void;
}

function BarItem({ height, isActive, delay = 0, onPress }: BarItemProps) {
  const animatedHeight = useSharedValue(0);

  React.useEffect(() => {
    animatedHeight.value = withDelay(delay, withTiming(height, { duration: 800 }));
  }, [height, delay]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: `${Math.max(animatedHeight.value, 8)}%`, // Minimum 8% height for visibility
    };
  });

  return (
    <Pressable onPress={onPress} className="flex-1 flex flex-col justify-end items-center h-full">
      <Animated.View
        className={cn(
          "w-full rounded-t-lg min-h-[8px]",
          isActive
            ? "bg-primary"
            : "bg-primary/40"
        )}
        style={[
          animatedStyle,
          {
            maxWidth: 24, // Limit bar width for better appearance
          },
          isActive && {
            shadowColor: '#F4C753',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 2,
          }
        ]}
      />
    </Pressable>
  );
}
