import { cn } from '@/lib/utils';
import React from 'react';
import { Pressable, PressableProps, Text } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

interface ButtonProps extends PressableProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function Button({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className, 
  ...props 
}: ButtonProps) {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    scale.value = withTiming(0.95, { duration: 100 });
  };

  const handlePressOut = () => {
    scale.value = withTiming(1, { duration: 100 });
  };

  const baseClasses = "rounded-full font-bold transition-all duration-200";
  
  const variantClasses = {
    primary: "bg-primary shadow-lg",
    secondary: "bg-surface-light dark:bg-surface-dark text-label-secondary-light dark:text-label-secondary-dark",
    ghost: "bg-transparent text-label-secondary-light dark:text-label-secondary-dark"
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  return (
    <AnimatedPressable
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      style={animatedStyle}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      {...props}
    >
      {typeof children === 'string' ? (
        <Text className={cn(
          "font-bold text-center",
          variant === 'primary' ? "text-black" : ""
        )}>{children}</Text>
      ) : (
        children
      )}
    </AnimatedPressable>
  );
}
