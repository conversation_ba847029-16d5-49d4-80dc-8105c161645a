import { cn } from '@/lib/utils';
import React from 'react';
import { View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withTiming } from 'react-native-reanimated';

interface ProgressBarProps {
  progress: number; // 0-100
  className?: string;
  barClassName?: string;
  delay?: number;
}

export function ProgressBar({ progress, className, barClassName, delay = 0 }: ProgressBarProps) {
  const animatedProgress = useSharedValue(0);

  React.useEffect(() => {
    animatedProgress.value = withDelay(delay, withTiming(progress, { duration: 1000 }));
  }, [progress, delay]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${animatedProgress.value}%`,
    };
  });

  return (
    <View className={cn("w-full bg-separator-light/32 dark:bg-separator-dark/32 rounded-full h-3", className)}>
      <Animated.View
        className={cn("h-3 rounded-full shadow-sm", barClassName)}
        style={[
          animatedStyle,
          {
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
          }
        ]}
      />
    </View>
  );
}
