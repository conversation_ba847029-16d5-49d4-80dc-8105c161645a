/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./App.tsx", "./components/**/*.{js,jsx,ts,tsx}", "./app/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        primary: "#F4C753",
        "background-light": "#f8f7f6",
        "background-dark": "#000000",
        "card-light": "#ffffff",
        "card-dark": "#1C1C1E",
        "surface-light": "#f2f2f7",
        "surface-dark": "#2c2c2e",
        "elevated-light": "#ffffff",
        "elevated-dark": "#3a3a3c",
        "separator-light": "#c6c6c8",
        "separator-dark": "#38383a",
        "label-primary-light": "#000000",
        "label-primary-dark": "#ffffff",
        "label-secondary-light": "#3c3c43",
        "label-secondary-dark": "#ebebf5",
        "label-tertiary-light": "#3c3c43",
        "label-tertiary-dark": "#ebebf5",
        "system-blue": "#007AFF",
        "system-green": "#34C759",
        "system-red": "#FF3B30",
        "system-orange": "#FF9500",
        "system-yellow": "#FFCC00",
        "system-purple": "#AF52DE",
        "system-pink": "#FF2D92",
        "system-teal": "#5AC8FA"
      },
      fontFamily: {
        display: ["Inter", "system-ui", "sans-serif"]
      },
      borderRadius: {
        DEFAULT: "0.5rem",
        lg: "1rem",
        xl: "1.5rem",
        "2xl": "2rem",
        full: "9999px"
      },
      backdropBlur: {
        xs: "2px",
        sm: "4px",
        DEFAULT: "8px",
        md: "12px",
        lg: "16px",
        xl: "24px",
        "2xl": "40px",
        "3xl": "64px"
      },
      opacity: {
        '02': '0.02',
        '04': '0.04',
        '06': '0.06',
        '08': '0.08',
        '12': '0.12',
        '16': '0.16',
        '24': '0.24',
        '32': '0.32',
        '68': '0.68',
        '72': '0.72',
        '76': '0.76',
        '84': '0.84',
        '88': '0.88',
        '92': '0.92',
        '96': '0.96'
      }
    },
  },
  plugins: [],
}