import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React from 'react';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarActiveTintColor: '#F4C753',
        tabBarInactiveTintColor: '#9CA3AF',
        tabBarStyle: {
          position: 'absolute',
          height: 56,
          borderTopWidth: 0.5,
          borderTopColor: 'rgba(0,0,0,0.08)',
          backgroundColor: 'rgba(255,255,255,0.96)',
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home" size={size ?? 22} color={color as string} />
          ),
        }}
      />
      <Tabs.Screen
        name="focus"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="time" size={size ?? 22} color={color as string} />
          ),
        }}
      />
      <Tabs.Screen
        name="track"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="bar-chart" size={size ?? 22} color={color as string} />
          ),
        }}
      />
      <Tabs.Screen
        name="achieve"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="trophy" size={size ?? 22} color={color as string} />
          ),
        }}
      />
      <Tabs.Screen
        name="tasks"
        options={{
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="list" size={size ?? 22} color={color as string} />
          ),
        }}
      />
    </Tabs>
  );
}
