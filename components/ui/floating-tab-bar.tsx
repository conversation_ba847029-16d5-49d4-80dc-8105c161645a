import React from 'react';
import { View, Pressable, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';

interface TabItem {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconFocused: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  isFocused: boolean;
}

interface FloatingTabBarProps {
  tabs: TabItem[];
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function FloatingTabBar({ tabs }: FloatingTabBarProps) {
  const insets = useSafeAreaInsets();

  const TabButton = ({ tab, index }: { tab: TabItem; index: number }) => {
    const scale = useSharedValue(1);
    const opacity = useSharedValue(tab.isFocused ? 1 : 0.6);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: scale.value }],
        opacity: opacity.value,
      };
    });

    const backgroundAnimatedStyle = useAnimatedStyle(() => {
      const backgroundColor = interpolate(
        tab.isFocused ? 1 : 0,
        [0, 1],
        [0, 1]
      );
      
      return {
        backgroundColor: `rgba(244, 199, 83, ${backgroundColor * 0.15})`,
        transform: [{ scale: tab.isFocused ? 1 : 0.8 }],
      };
    });

    const handlePressIn = () => {
      scale.value = withSpring(0.95, {
        damping: 15,
        stiffness: 300,
      });
    };

    const handlePressOut = () => {
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 300,
      });
    };

    const handlePress = () => {
      tab.onPress();
      // Haptic feedback would go here if needed
    };

    React.useEffect(() => {
      opacity.value = withTiming(tab.isFocused ? 1 : 0.6, {
        duration: 200,
      });
    }, [tab.isFocused]);

    return (
      <AnimatedPressable
        style={[animatedStyle, { flex: 1, alignItems: 'center' }]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        accessibilityRole="tab"
        accessibilityState={{ selected: tab.isFocused }}
      >
        <Animated.View
          style={[
            backgroundAnimatedStyle,
            {
              position: 'absolute',
              width: 48,
              height: 48,
              borderRadius: 24,
              top: 8,
            },
          ]}
        />
        <View style={{ paddingVertical: 12 }}>
          <Ionicons
            name={tab.isFocused ? tab.iconFocused : tab.icon}
            size={24}
            color={tab.isFocused ? '#F4C753' : '#9CA3AF'}
          />
        </View>
      </AnimatedPressable>
    );
  };

  const containerStyle = {
    position: 'absolute' as const,
    bottom: insets.bottom + 16,
    left: 16,
    right: 16,
    height: 64,
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  };

  if (Platform.OS === 'ios') {
    return (
      <View style={containerStyle}>
        <BlurView
          intensity={80}
          tint="light"
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 8,
          }}
        >
          {tabs.map((tab, index) => (
            <TabButton key={tab.name} tab={tab} index={index} />
          ))}
        </BlurView>
      </View>
    );
  }

  // Fallback for Android and other platforms
  return (
    <View
      style={[
        containerStyle,
        {
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
        },
      ]}
    >
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 8,
        }}
      >
        {tabs.map((tab, index) => (
          <TabButton key={tab.name} tab={tab} index={index} />
        ))}
      </View>
    </View>
  );
}
